# Go 面试题

## 基础

### make 和 new 的区别

new：将一块内存区域清零，返回指向该内存的指针，所有类型支持
make：初始化一块内存区域，并初始化内部数据结构，仅用于初始化 slice、map 和 channel 这三种内置引用类型，返回值本身

### 数组和切片的区别

1. 类型：数组是值类型，定长，在编译阶段就必须指定长度，不能动态指定长度。 切片是引用类型，不定长的，可以在运行时动态指定长度。
2. 底层结构：一段连续的数据；`{ ptr, len, cap }`。也就是说拷贝的话数组时间复杂度是 o(n)，拷贝切片时间复杂度是 o(1)

### for range 的时候它的地址会发生变化么？

不会，for _, v := range mySlice 中的 v，它的内存地址在整个循环过程中是固定不变的。解决方案是在内部创建一个临时变量

### go defer，多个 defer 的顺序，defer 在什么时机会修改返回值？

- defer 是函数体执行完毕，按照先进后出的顺序额外执行的逻辑。
- 不能修改函数匿名返回值	`func() (int, error)`，因为返回值发生了拷贝
- 可以函数修改命名返回值 `func() (result int, err error)`

### 能介绍下 rune 类型吗？

- rune 代表一个字符，底层是 int32，支持 UTF-8，for range 字符串遍历时就是按照 rune 来遍历的。它返回的第二个值就是 rune 类型。
- byte 代表一个字节，底层是 uint8，常用于处理二进制数据流

### golang 中解析 tag 是怎么实现的？反射原理是什么？
- tag 是定义在结构体字段的元数据，可以通过反射来解析，类似于 typescript 中的 metadata 的概念。
- 反射的原理：程序在运行时（Runtime）检查自身结构和状态的能力，并可以对其进行修改。

### 调用函数传入结构体时，应该传值还是指针？

- 大多数情况下，应该传递指针。
- 性能考量：函数传值，会将值拷贝一份（如果结构体里有 slice，那么只会拷贝 slice 的描述符，而不会拷贝底层的数组）。考虑性能应该传指针。
- 功能需求：通过传指针可以修改结构体

### 讲讲 Go 的 slice 底层数据结构和一些特性？

- 类型：引用类型，它的 header 结构是：`{ ptr, len, cap }`
- 切片操作：s[1:3] 这样的操作，会创建一个新的 slice header，但仍然指向同一个底层数组
- append：如果容量足够，它会直接在底层数组上修改。如果容量不足，就会触发扩容。
- 扩容策略：len < 256，翻倍；容量增大，增长因子会变小，趋近于 1.25

### 讲讲 Go 的 select 底层数据结构和一些特性？

- channel 底层结构
  - 每个 channel 内部都维护着两个独立的等待队列，recvq 和 sendq，它们本质上都是双向链表。
  - 每一个节点都是一个 sudog 结构体，这个 sudog 代表一个被阻塞的 goroutine。
  - 当一个 goroutine 因为 channel 满了而无法发送，或者因为 channel 空了而无法接收时，这个 goroutine 就会被封装成一个 sudog 结构体，并加入到相应的等待队列中排队。

- 原理
  - 随机轮询：随机打乱 case 顺序，依次 channel 是否准备好了，如果有，就执行
  - 注册挂起：如果检查完了没有准备好的 channel，并且没有 default，select 会把当前 goroutine 注册到所有 case 对应的 channel 等待队列上，然后让当前 goroutine 挂起并休眠
  - 唤醒并清理：当其中任意一个 channel 变得可用时，它会唤醒这个 goroutine。goroutine 醒来后，做的第一件事就是把自己从所有等待队列中移除，以防被重复唤醒，然后才执行相应 case 的代码。

- 特性：同时等待多个 channel 操作，通过 default case 实现非阻塞操作，当多个 case 就绪时会进行伪随机选择。

### 讲讲 Go 的 defer 底层数据结构和一些特性？

1.12 及之前
- defer 通过后进先出的链表来实现，每个 goroutine 内部有个指向 defer 链表头部的指针，defer 的结构如下
  - fn：函数
  - sp：栈
  - args：参数
  - link：下一个 defer 指针
- 每次 defer 会导致一次堆内存分配，在高频调用的函数中会带来明显的性能开销和 GC（垃圾回收）压力

1.13
- 为了优化性能，如果 defer 数量是已知的（通过代码静态分析），那么这些 defer 会保存在栈中。
- 其他情况比如说 defer 在未知次数的 for 循环中或者条件语句中会回退到之前的版本

1.14
- 引入开放编码优化，如果函数只有一个 return 语句，而且函数体代码简单，不会发生 panic （比如数组越界空指针等等操作），那么 defer 的执行体会内联到函数 return 语句之前执行。性能开销几乎为零。

### 单引号，双引号，反引号的区别？

| 特性 | 单引号 (`'`) | 双引号 (`"`) | 反引号 (`` ` ``) |
| :--- | :--- | :--- | :--- |
| **Go 类型** | `rune` (本质是 `int32`) | `string` | `string` |
| **内容** | 单个字符 | 字符串 | 字符串 |
| **转义字符** | 支持有限转义（如 `'\n'`, `'\''`） | **会解析**（如 `\n`, `\t`, `\"`） | **不会解析**（所见即所得） |

## Map

### map 使用注意的点，是否并发安全？

- 无序：遍历时 map 无序，保证有序可以使用 ordered map
- 必须初始化，否则 panic
- 访问不存在的键时会返回零值
- 判断是否存在用第二个返回值
- 引用类型，函数传值的时候是传的描述符，修改时会影响原始的 map
- 并发不安全，可以使用 sync.map

### map 中删除一个 key，它的内存会释放么？

- 处于性能考虑 delete 一个 key 不会释放 map 内部数据结构的内存，map 的容量不会收缩，避免因频繁增删导致的内存分配和释放开销。
- 被删除的 key/value 对象本身的内存，如果不再有其他引用，是可以被 GC 回收的。不释放的是 map 的桶（bucket）结构占用的内存。
- 如果确实需要回收 map 内部数据结构的这部分内存，标准的做法是创建一个新的 map 并将有效元素复制过去，让 GC 来回收旧的 map。


### 怎么处理对 map 进行并发访问？有没有其他方案？ 区别是什么？

- map + sync.RWMutex
  - 写：写锁是排他的，一旦加上，其他任何 goroutine（无论是读还是写）都必须等待。
  - 读：读锁是共享的，可以有多个 goroutine 同时获取读锁进行读取。
  - 在读多写少的场景下，性能非常好，因为多个读操作可以并行。
  - 强类型
- sync.map
  - 内部使用读写分离的两个 map，一个无锁的只读 read map 作为缓存，一个加锁的可写 dirty map 作为主存储。实现了空间换时间上的性能优化。
  - 读：先尝试在 read map 中读取，如果不存在再加锁到 dirty map 中读取。
  - 性能高的场景在于读多写少，或者键集合不重复的场景下，会比 map + sync.RWMutex 性能高，因为后者读操作会有个加锁的性能开销
  - 存取都是 interface{}
  - 缓存的升级机制：misses >= len(dirty map)，即如果多次 miss，次数超过了 dirty map 长度，会将 dirty map 升级为 read map，使用一个新的 map 作为 dirty map，同时将 misses 置为 1

### nil map 和空 map 有何不同？

- nil map：map 底层数据结构的内存未初始化，写操作会 panic
- 空 map：初始化了，但是没有键值对

### map 的数据结构是什么？是怎么实现扩容？

数据结构
- map 底层指向了一个 hmap
  - hmap
    - count：长度
    - B：桶的数量等于 2^B
    - buckets：指向桶数组，大小为 2^B
    - oldbuckets：扩容时指向旧的桶数组，大小为 2^(B-1)
  - 桶
    - tophash：键的高 8 位 hash
    - keys：键数组，长度为 8
    - values：值数组，长度为 8
    - overflow：指向溢出桶的指针
- 扩容条件
    - 元素数量 / 桶数量 > 6.5，桶的数量会变成原来的 2 倍
    - 溢出桶数量过多。等量扩容，桶的数量不变，重新整理键值对
- 扩容机制（渐进式扩容）
  - 生成一个新桶，将 oldbuckets 指向当前桶，将 buckets 指向新桶
  - 后续写操作：每次都会迁移一两个桶
  - 后续写操作：现在新桶里面找，如果旧桶不为 nil，在旧桶里找
  - 完成以后把旧桶置为 nil

### slices能作为map类型的key吗？

不能，map 的键必须是可比较的，而 slice 是不可比较的

不能作为 map key 的类型包括：

- slice
- map
- function
- 包含以上类型的结构体、数组

## Context

### context 结构是什么样的？context 使用场景和用途？

结构
- `Deadline() (deadline time.Time, ok bool)`：截止时间，是否有截止时间
- `Done() <-chan struct{}`：只读 channel，用于接收“完成”信号
- `Err() error`：Done() 的 channel 关闭后，被取消返回`Canceled`，超时返回 `DeadlineExceeded`
- `Value(key interface{}) interface{}`：Context 中获取一个键值对数据

用途
- 取消信号
- 超时控制
- 传递元数据

### channel 是否线程安全？锁用在什么地方？

- 是的
- 内部使用 sync.Mutex，为什么不用读写锁？因为内部都是涉及到对内部状态的修改，而读写锁适用于“读多写少”的场景

### nil、关闭的 channel、有数据的 channel，再进行读、写、关闭会怎么样？

| Channel 状态 | 读操作 (`<-ch`) | 写操作 (`ch <- val`) | 关闭操作 (`close(ch)`) |
| :--- | :--- | :--- | :--- |
| **`nil`** | ⏸️ **永久阻塞 (Blocks forever)** | ⏸️ **永久阻塞 (Blocks forever)** | 🔴 **Panic** |
| **已关闭 (Closed)** | ✅ **立即返回零值** (Non-blocking) | 🔴 **Panic** | 🔴 **Panic** |
| **打开且有数据/有缓冲 (Open, has data/buffer)** | ✅ **读取一个值** (Non-blocking) | (取决于缓冲区是否已满) | ✅ **成功关闭** |
| **打开但无数据/缓冲已满 (Open, no data/buffer full)** | ⏸️ **阻塞** (Blocks) | ⏸️ **阻塞** (Blocks) | ✅ **成功关闭** |

- 在 select 语句中，将 channel 设为 nil，那么会永远跳过这个分支语句执行。
- 安全关闭：sync.WaitGroup

### 向 channel 发送数据和从 channel 读数据的流程是什么样的？

**发送 (`ch <- data`) 流程：**

1.  **有接收者在等吗？**
  *   **是**：直接把数据给它，唤醒它。**完成**。
  *   **否** ↓

2.  **缓冲区有空位吗？**
  *   **是**：把数据放进缓冲区。**完成**。
  *   **否** ↓

3.  **阻塞**：进入发送等待队列，挂起当前 goroutine，等待被接收者唤醒。

---

**接收 (`<- ch`) 流程：**

1.  **有发送者在等吗？**
  *   **是**：直接拿走它的数据，唤醒它。（如果缓冲区满了，顺便把缓冲区的数据也拿走一个，再把等待的数据填进去）。**完成**。
  *   **否** ↓

2.  **缓冲区有数据吗？**
  *   **是**：从缓冲区拿一个数据。**完成**。
  *   **否** ↓

3.  **Channel 关闭了吗？**
  *   **是**：立即拿到一个零值。**完成**。
  *   **否** ↓

4.  **阻塞**：进入接收等待队列，挂起当前 goroutine，等待被发送者唤醒或 channel 被关闭。

## GMP

### 什么是 GMP？

- m: 线程的抽象
- p：cpu 的抽象，存放 g 队列，p 的数量默认等于系统的 CPU 核心数
- g：协程

GMP 是一个 m:n 模型，即一个 m 可以处理多个的 g

- 基本操作
  - M 找到一个空闲的 P 绑定
  - M 顺序运行 P 队列中的 G
- 抢占式调度
  - 本质是确保所有的 g 有机会执行，不能因为某个 g 执行时间过长而一直占用 cpu 导致其他 g 得不到运行的机会。
  - p 会给 g 分配一个时间片，如果 g 运行时间超过这个时间片，p 会把它放回队列中去（保存栈状态和上下文），拿一个新的 g 来运行。
- 阻塞操作
  - M 和 G 进入阻塞状态
  - P 和 M 解绑，并在自身队列或者其他 P 的队列中拿一个 G
  - 寻找一个空闲的 M，或者创建一个新的 M 来 继续执行协程
  - go 后台会启动一个 poller，轮询检查阻塞状态，一但检查到阻塞任务结束，会把阻塞的 G 唤醒重新加入到队列中

### 进程线程协程的区别

| 特性 | 进程 (Process) | 线程 (Thread) | 协程 (Coroutine) |
| :--- | :--- | :--- |:---------------|
| **定义** | 资源分配的基本单位 | CPU 调度的基本单位 | 用户态的、更轻的线程     |
| **资源隔离** | 独立内存空间，完全隔离 | 共享进程资源 | 运行在线程之上，共享进程资源 |
| **调度方** | 操作系统 | 操作系统 | 用户程序 / 语言运行时   |
| **调度方式** | 抢占式 | 抢占式 | 协作式 (Go 近似抢占)  |
| **切换开销** | **极高** (涉及内核) | **较高** (涉及内核) | **极低** (不涉及内核) |
| **数量级** | 数十 / 上百 | 上百 / 上千 | **数十万 / 上百万**  |

### 抢占式调度是如何抢占的？

Go 的抢占调度由一个叫 `sysmon` 的后台线程驱动

- sysmon 定期检查运行时间超过 10ms 的 G
- sysmon 向运行该 G 的系统线程 M 发送一个信号
- M 收到信号后，会给这个 G 打一个需要被强占的标记
- G 在下一次函数调用时，会检查到这个标记，然后主动调用调度器，让出执行权

### M 和 P 的数量问题？

- P 的数量：由 GOMAXPROCS 决定，默认等于 CPU 逻辑核心数。数量相对固定
- M 的数量：动态调整，上限默认为 10,000。有创建、休眠、销毁等生命周期。

## 锁相关

### 除了 mutex 以外还有那些方式安全读写共享变量？

- sync.Mutex/RWMutex
- Channel
- sync/atomic
- sync.Once
- sync.Map

### Go 如何实现原子操作？

sync/atomic 内部函数题代码被翻译成汇编原子指令执行

### Mutex 是悲观锁还是乐观锁？悲观锁、乐观锁是什么？

- Mutex 是悲观锁
- 悲观锁：假设会发生冲突，加锁再执行操作
- 乐观锁：假设不会发生冲突，先执行操作，在最后写入时验证数据是否被修改，比如（sync/atomic）
  - 写入验证
    - CAS：比较内存中的值是否等于自己当初读取的那个“旧值”。如果是，就更新为“新值”。
    - 在数据库应用场景中，加一个 version 字段，读的时候加一个 version 读出来，写的时候将之前的 version 加入写入条件