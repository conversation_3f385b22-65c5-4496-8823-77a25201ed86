# 比特币有哪些分叉链，产生的原因是什么?

## 分叉类型分析

### 硬分叉 (Hard Fork)
**定义**: 改变协议规则，不兼容旧节点，会导致新链产生

基本上都是由容量，挖矿 hash 算法，意见分歧导致的硬发叉：BCH、BSV、BTG、BCD

### 软分叉 (Soft Fork)
**定义**: 收紧协议规则，新节点兼容旧节点，不会产生新链

- 引入了 P2SH 地址：用户可以将比特币发送到一个脚本的哈希地址
- 在区块头加入 block height 信息
- 引入时间锁概念，使得交易可以在设定的时间戳之前无法被花费
- 引入 SegWit，新增了 P2WPKH 和 P2WSH地址类型，例如解决交易延展性攻击问题
- 引入 Taproot，新增了 P2TR 地址类型
  - 引入 Schnorr 签名算法，支持高校批量验证，替代原来的 ECDSA
  - 引入 MAST 技术，将复杂脚本隐藏在 Merkle 树中，只有实际执行的路径会被暴露，提高隐私

