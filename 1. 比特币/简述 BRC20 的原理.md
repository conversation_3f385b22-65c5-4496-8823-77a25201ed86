# 简述 BRC20 的原理

## 什么是 BRC20？

由 Domo 设计的协议，BRC20 是基于比特币 Ordinals 协议的同质化代币标准，通过在比特币交易中嵌入 JSON 数据来实现代币功能。

## 核心原理

Ordinals（序数） 协议：给每个聪（satoshi）分配唯一序号。
Inscription（铭文）: 在聪上刻写任意数据（图片、文本、JSON等）大小通常不超 4mb（taproot 限制），这个动作叫做铭刻，数据是铭文

铭文的创建流程：

1. 创建一笔特殊的‘自己转给自己’的交易（价值非常小一般是 546 聪）。它通过在输入的见证数据中嵌入由 opcode（OP_FALSE OP_IF） 包裹的数据（铭文），利用了 Taproot 升级。
2. 这段数据不被矿工执行，会永久存在于链上，但会被链下的索引器解析计算。
3. Ordinals 协议规定交易中，第一个输入的见证数据里包含的铭文，会被分配给这笔交易的第一个输出的第一个聪。
4. 这笔输出所代表的 UTXO 是该铭文的载体
5. BRC-20 只是将铭刻的内容标准化为 JSON 格式，从而赋予其同质化代币的意义。

## 数据结构
- Deploy（部署）
```json
{
  "p": "brc-20",
  "op": "deploy",
  "tick": "EXAMPLE",    // 代币名称（4字符）
  "max": "21000000",    // 最大供应量
  "lim": "1000"         // 单次铸造限制
}
```

- Mint（铸造）
```json
{
  "p": "brc-20", 
  "op": "mint",
  "tick": "EXAMPLE",
  "amt": "1000"         // 铸造数量
}
```

- Transfer（转账）：转账分为两步，第一步是创建“可转移铭文”，相当于是一张支票，第二步是通过一笔普通的比特币交易，发送到接收者的地址。
```json
{
  "p": "brc-20",
  "op": "transfer", 
  "tick": "EXAMPLE",
  "amt": "100"          // 转账数量
}
```

第一个基于 brc20 协议部署的代币：ordi。

sats（基于 satoshi 命名）的代币，其他的代币计价基于 sats，也就是说其他代币的价格等于 （其他代币的 sats 价） * （sats 美元价）

## 特点

- 只支持 taproot 类型的地址（支持更灵活的脚本），虽然隔离见证地址也支持见证数据字段，但其脚本结构是固定的，不允许像 Taproot 那样嵌入任意的、可被忽略的脚本数据。
- 不可变，一旦铭刻就不可变
- 状态计算通过链下索引服务器计算，在链下，去中心化程度低
- 由于使用了 json 作为数据传送，交易手续费比普通交易要高
- 交易所支持 BRC20 的话需要对 utxo 进行区分，如果这笔 utxo 中承载了 BRC20 数据的话需要和普通转账分开

## 符文（Runes）

为什么出现：

BRC20 的缺点：
1. 网络污染：铭刻产生了大量无意义的 utxo
2. 去中心化程度低：依赖链下索引器

### 什么是 OP_RETURN

比特币的交易输出分为可花费输出（可解的谜题），也就是 UTXO；不可花费输出（不可解谜题），使用了 OP_RETURN 的交易输出就是不可花费输出。

创建一笔不可花费输出，脚本以 OP_RETURN 开头的：

```json
{
  "value": 0, // 按照Runes协议标准，价值必须为0
  "scriptPubKey": "OP_RETURN <Your_Data_Here>" // (e.g., Runes protocol data 'R...')
}
```

OP_RETURN 会中止脚本的执行并导致该脚本被判定为失败。但这并不会导致整个交易被标记为无效。如果交易的其他部分（如签名、输入等）都是有效的，那么这笔交易仍然是有效的，可以被打包进区块。

当比特币节点看到 OP_RETURN 时，它立刻知道这个输出永远不可能被花费。因此，节点永远不会将 这个 txout 加入到它的 UTXO 集合中。

Runes协议规定，写在 OP_RETURN 里的数据被称为 Runestone（符文石）

TODO：为什么 OP_RETURN 不能作为 memo 字段？

### 实现原理

操作类型：
- Etching (蚀刻)：相当于 BRC-20 的 deploy，用于创建一种新的符文，定义它的名称、符号、精度等。
- Mint (铸造)：在蚀刻时可以设定一个开放的铸造期，允许用户通过交易来铸造新的符文。
- Transfer (转移)：如上所述，通过 Runestone 来分配。”

以转账为例：

1. Runes 协议检查 vin 包含哪些符文代币 
2. 检查 vout 是否有 OP_RETURN 输出，如果有，检查 OP_RETURN 输出是否是 Runestone，这个 Runestone 会明确如何将 vin 中的代币分配到哪些索引下的 vout
3. 如果 Runestone 没有明确指定分配，所有未分配的符文会自动流向第一个非 OP_RETURN 的输出

## 对比铭文（以BRC-20为代表）和符文

- 网络：符文不会产生垃圾 utxo，符文产生的不可花费输出会被矿工丢弃
- 转账：铭文转账是需要先创建一笔转账铭文，有点类似于支票的概念，然后将这个转账铭文再发送一笔交易到对方
- 中心化：符文中心化更高
- 数据：铭文更大，铭文数据写在见证数据里面，支持更大的数据量，如 NFT；符文更小（80字节）因此专注于FT，符文数据写在锁定脚本里面
- 地址：符文支持所有类型的地址，因为符文数据在 OP_RETURN 输出里，创建 OP_RETURN 输出与输入地址输出地址（他不需要输出地址）类型无关；但是铭文仅仅支持 taproot 地址（因为它支持复杂脚本）
