定义：未花费交易输出

- 结构

```json
{
  "txid": "0b8f0d798f01c5b6fba4cf231ffb8b9677e057b4c1b431b7ad9f89f043b72b5b",
  "vout": 0,
  "value": 50000000,
  "scriptPubKey": {
    "asm": "OP_DUP OP_HASH160 89abcdefab... OP_EQUALVERIFY OP_CHECKSIG",
    "hex": "76a91489abcdefab...88ac",
    "addresses": ["1A1zP1eP5QGefi2DMPTfTL5SLmv7DivfNa"]
  },
  "locktime": 0
}
```

- txid: 交易 hash
- vout: 在这笔交易中的输出索引
- value: 金额
- scriptPubKey: 指明了花费这笔 utxo 的条件
  - asm 是汇编语言表示的操作码（opcode），
  - hex 是 opcode 的十六进制表示
  - addresses 是接收地址
- locktime: 让交易在指定的日期后才开始生效，可以是时间戳或者区块高度。locktime >= 5亿是绝对时间，否则是区块高度

交易时，引用 txid 和 vout 即可指定一笔 UTXO

不同类型地址所使用的 scriptPubKey 的内容不同:

- p2pkh: `OP_DUP OP_HASH160 <pubKeyHash> OP_EQUALVERIFY OP_CHECKSIG`，先验证公钥是否匹配，然后用公钥验证签名
  - OP_DUP：复制栈顶元素（通常是公钥）。
  - OP_HASH160：对公钥进行 SHA-256 然后是 RIPEMD-160 哈希，得到公钥的哈希值。
  - （上面2步就是地址生成流程中的公钥 hash）
  - <公钥哈希>：接收者的公钥哈希。
  - OP_EQUALVERIFY：验证栈顶的公钥哈希和提供的 <公钥哈希> 是否匹配。
  - OP_CHECKSIG：验证签名，确保只有拥有正确私钥的人才能花费这笔 UTXO。
- p2sh: `OP_HASH160 <脚本哈希> OP_EQUAL` 
  - OP_HASH160 对栈顶元素进行 SHA-256 然后是 RIPEMD-160 哈希，得到脚本的哈希值。
  - <脚本哈希>：目标脚本的哈希值。
  - OP_EQUAL：验证栈顶的脚本哈希和提供的 <脚本哈希> 是否匹配。如果匹配，执行后续操作；否则，交易无效。
- p2wpkh: `OP_0 <pubKeyHash>`
- p2wsh: `OP_0 <redeemScriptHash>`