
# 比特币不同类型地址的特点

## P2PKH (Pay-to-Public-Key-Hash)
- **格式**: 以 `1` 开头
- **编码流程**: 
  1. 公钥 → SHA-256 → RIPEMD-160 得到公钥哈希（20字节）
  2. 添加版本前缀 0x00
  3. Base58Check 编码
- **特点**: 最传统的地址类型
- **优点**: 兼容性最好，所有钱包都支持
- **缺点**: 交易费用相对较高

## P2SH (Pay-to-Script-Hash)  
- **格式**: 以 `3` 开头
- **编码流程**:
  1. 脚本 → SHA-256 → RIPEMD-160 得到脚本哈希（20字节）
  2. 添加版本前缀 0x05
  3. Base58Check 编码
- **特点**: 支持多重签名等复杂脚本
- **优点**: 支持复杂交易逻辑，费用由接收方承担
- **缺点**: 脚本复杂度有限制

## P2WPKH (Pay-to-Witness-Public-Key-Hash)
- **格式**: 以 `bc1q` 开头
- **编码流程**:
  1. 公钥 → SHA-256 → RIPEMD-160 得到公钥哈希（20字节）
  2. 使用 Bech32 编码，人类可读部分为 "bc"，见证版本为 0
- **特点**: SegWit 原生地址
- **优点**: 交易费用最低，抗交易延展性攻击
- **缺点**: 部分老钱包不支持

## P2WSH (Pay-to-Witness-Script-Hash)
- **格式**: 以 `bc1q` 开头，32字节更长
- **编码流程**:
  1. 脚本 → SHA-256 得到脚本哈希（32字节）
  2. 使用 Bech32 编码，见证版本为 0
- **特点**: SegWit 脚本哈希
- **优点**: 费用低，支持大型多重签名
- **缺点**: 兼容性问题

## P2TR (Pay-to-Taproot)
- **格式**: 以 `bc1p` 开头
- **编码流程**:
  1. Taproot 输出 → 32字节公钥
  2. 使用 Bech32m 编码，见证版本为 1
- **特点**: 最新的 Taproot 地址
- **优点**: 隐私性最好，费用最低，脚本灵活性最高（带动了后续 BRC20 的出现），使用 Schnorr 算法生成签名，支持多签
- **缺点**: 需要最新钱包支持
